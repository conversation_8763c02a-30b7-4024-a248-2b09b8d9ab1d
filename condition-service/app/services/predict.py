from app import logger
from app.core.utils.s3 import get_file
from app.core.utils.yolo_model_runner import Yo<PERSON><PERSON>odel<PERSON>unner
from app.models.predict_request import PredictRequest


class PredictService:
    def predict(self,  r: PredictRequest):
        logger.info(f"Predict: {r.s3_key}")

        local_image_path = get_file(r.bucket_name, r.s3_key)
        logger.info(f"local_image_path: {local_image_path}")


        for section, damage_types in r.parameters.condition:
            for damage_type in damage_types:
                model_s3_key = f"models/best_{r.parameters.type}_{section}_{damage_type}.pt"
                runner = Yo<PERSON>ModelRunner(r.bucket_name, model_s3_key)
                result = runner.run_prediction(local_image_path, section, damage_type)
                logger.info(f"result: {result}")









